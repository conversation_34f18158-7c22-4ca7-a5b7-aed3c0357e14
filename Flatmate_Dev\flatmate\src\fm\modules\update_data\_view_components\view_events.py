#!/usr/bin/env python3
"""
View Events for Update Data Module

Defines all local events used for internal module coordination.
Separate from global events to maintain clean separation of concerns.
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime


class ViewEvents(Enum):
    """
    Local view events for Update Data module.
    
    These events handle internal module coordination and should not
    be confused with global events that cross module boundaries.
    """
    
    # ==========================================
    # USER ACTION EVENTS (View → Presenter)
    # ==========================================
    
    # Source selection events
    SOURCE_SELECT_REQUESTED = "source_select_requested"
    SOURCE_OPTION_CHANGED = "source_option_changed"
    
    # Destination selection events  
    DESTINATION_SELECT_REQUESTED = "destination_select_requested"
    SAVE_OPTION_CHANGED = "save_option_changed"
    
    # Processing control events
    PROCESS_REQUESTED = "process_requested"
    CANCEL_REQUESTED = "cancel_requested"
    
    # Configuration events
    UPDATE_DATABASE_CHANGED = "update_database_changed"
    
    # ==========================================
    # BUSINESS EVENTS (Presenter → State)
    # ==========================================
    
    # Source discovery events
    SOURCE_DISCOVERED = "source_discovered"
    SOURCE_DISCOVERY_FAILED = "source_discovery_failed"
    
    # Destination configuration events
    DESTINATION_CONFIGURED = "destination_configured"
    DESTINATION_CONFIGURATION_FAILED = "destination_configuration_failed"
    
    # Processing lifecycle events
    PROCESSING_STARTED = "processing_started"
    PROCESSING_PROGRESS = "processing_progress"
    PROCESSING_COMPLETED = "processing_completed"
    PROCESSING_FAILED = "processing_failed"
    
    # General business events
    BUSINESS_ERROR = "business_error"
    VALIDATION_ERROR = "validation_error"
    
    # ==========================================
    # STATE EVENTS (State → View)
    # ==========================================
    
    # UI state management
    UI_STATE_CHANGED = "ui_state_changed"
    STATUS_MESSAGE_CHANGED = "status_message_changed"
    PROCESS_BUTTON_STATE_CHANGED = "process_button_state_changed"
    
    # Display events
    FILE_DISPLAY_UPDATED = "file_display_updated"
    PROGRESS_DISPLAY_UPDATED = "progress_display_updated"
    
    # ==========================================
    # DIALOG EVENTS (Bidirectional)
    # ==========================================
    
    # Folder dialogs
    FOLDER_DIALOG_REQUESTED = "folder_dialog_requested"
    FOLDER_DIALOG_COMPLETED = "folder_dialog_completed"
    
    # File dialogs
    FILES_DIALOG_REQUESTED = "files_dialog_requested"
    FILES_DIALOG_COMPLETED = "files_dialog_completed"
    
    # Message dialogs
    ERROR_DIALOG_REQUESTED = "error_dialog_requested"
    SUCCESS_DIALOG_REQUESTED = "success_dialog_requested"
    CONFIRMATION_DIALOG_REQUESTED = "confirmation_dialog_requested"
    
    # ==========================================
    # MODULE LIFECYCLE EVENTS
    # ==========================================
    
    # Module activation/deactivation
    MODULE_ACTIVATED = "module_activated"
    MODULE_DEACTIVATED = "module_deactivated"
    
    # View lifecycle
    VIEW_INITIALIZED = "view_initialized"
    VIEW_REFRESHED = "view_refreshed"
    VIEW_CLEANUP = "view_cleanup"


class EventDataFactory:
    """
    Factory class for creating standardized event data structures.
    
    Provides type-safe, consistent data structures for all events
    and helps prevent data structure inconsistencies.
    """
    
    # ==========================================
    # USER ACTION DATA
    # ==========================================
    
    @staticmethod
    def source_select_request(selection_type: str, path: Optional[str] = None) -> Dict[str, Any]:
        """Create source selection request data."""
        return {
            'selection_type': selection_type,
            'path': path,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'user_action'
        }
    
    @staticmethod
    def process_request(context: Dict[str, Any]) -> Dict[str, Any]:
        """Create process request data."""
        return {
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'user_action'
        }
    
    @staticmethod
    def option_changed(option_type: str, old_value: Any, new_value: Any) -> Dict[str, Any]:
        """Create option change data."""
        return {
            'option_type': option_type,
            'old_value': old_value,
            'new_value': new_value,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'user_action'
        }
    
    # ==========================================
    # BUSINESS EVENT DATA
    # ==========================================
    
    @staticmethod
    def source_discovered(source_type: str, files: List[str], path: str) -> Dict[str, Any]:
        """Create source discovery data."""
        return {
            'source_type': source_type,
            'files': files,
            'path': path,
            'file_count': len(files),
            'timestamp': datetime.now().isoformat(),
            'event_category': 'business'
        }
    
    @staticmethod
    def destination_configured(destination_type: str, path: str) -> Dict[str, Any]:
        """Create destination configuration data."""
        return {
            'destination_type': destination_type,  # 'same_as_source' or 'custom'
            'path': path,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'business'
        }
    
    @staticmethod
    def processing_started(job_context: Dict[str, Any]) -> Dict[str, Any]:
        """Create processing started data."""
        return {
            'job_context': job_context,
            'file_count': len(job_context.get('files', [])),
            'timestamp': datetime.now().isoformat(),
            'event_category': 'business'
        }
    
    @staticmethod
    def processing_completed(success: bool, result: Dict[str, Any]) -> Dict[str, Any]:
        """Create processing completion data."""
        return {
            'success': success,
            'result': result,
            'processed_count': result.get('processed_count', 0),
            'timestamp': datetime.now().isoformat(),
            'event_category': 'business'
        }
    
    @staticmethod
    def business_error(error_type: str, message: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Create business error data."""
        return {
            'error_type': error_type,
            'message': message,
            'context': context or {},
            'timestamp': datetime.now().isoformat(),
            'event_category': 'business_error'
        }
    
    # ==========================================
    # UI STATE DATA
    # ==========================================
    
    @staticmethod
    def ui_state_update(
        can_process: bool, 
        status_message: str, 
        processing: bool = False,
        **additional_state
    ) -> Dict[str, Any]:
        """Create UI state update data."""
        return {
            'can_process': can_process,
            'status_message': status_message,
            'processing': processing,
            'archive_enabled': additional_state.get('archive_enabled', False),
            'process_button_text': additional_state.get('process_button_text', 'Process Files'),
            'timestamp': datetime.now().isoformat(),
            'event_category': 'ui_state',
            **additional_state
        }
    
    @staticmethod
    def files_display_update(files: List[str], source_path: str) -> Dict[str, Any]:
        """Create files display update data."""
        return {
            'files': files,
            'source_path': source_path,
            'file_count': len(files),
            'timestamp': datetime.now().isoformat(),
            'event_category': 'ui_display'
        }
    
    # ==========================================
    # DIALOG DATA
    # ==========================================
    
    @staticmethod
    def dialog_request(
        dialog_type: str, 
        title: str, 
        message: Optional[str] = None,
        **dialog_options
    ) -> Dict[str, Any]:
        """Create dialog request data."""
        return {
            'dialog_type': dialog_type,
            'title': title,
            'message': message,
            'options': dialog_options,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'dialog'
        }
    
    @staticmethod
    def dialog_response(
        dialog_type: str, 
        result: Any, 
        cancelled: bool = False
    ) -> Dict[str, Any]:
        """Create dialog response data."""
        return {
            'dialog_type': dialog_type,
            'result': result,
            'cancelled': cancelled,
            'timestamp': datetime.now().isoformat(),
            'event_category': 'dialog_response'
        }


# Event validation helpers
def validate_event_data(event_type: ViewEvents, data: Dict[str, Any]) -> bool:
    """
    Validate event data structure.
    
    Args:
        event_type: The event type being validated
        data: The event data to validate
        
    Returns:
        True if data is valid, False otherwise
    """
    if not isinstance(data, dict):
        return False
        
    # All events should have timestamp and category
    required_fields = ['timestamp', 'event_category']
    
    # Add specific validation based on event type
    if event_type in [ViewEvents.SOURCE_DISCOVERED, ViewEvents.FILES_DISPLAY_UPDATED]:
        required_fields.extend(['files', 'file_count'])
    elif event_type == ViewEvents.UI_STATE_CHANGED:
        required_fields.extend(['can_process', 'status_message'])
    elif event_type == ViewEvents.BUSINESS_ERROR:
        required_fields.extend(['error_type', 'message'])
        
    return all(field in data for field in required_fields)


def get_event_category(event_type: ViewEvents) -> str:
    """
    Get the category for an event type.
    
    Args:
        event_type: The event type
        
    Returns:
        The event category string
    """
    category_map = {
        # User actions
        ViewEvents.SOURCE_SELECT_REQUESTED: 'user_action',
        ViewEvents.PROCESS_REQUESTED: 'user_action',
        ViewEvents.CANCEL_REQUESTED: 'user_action',
        
        # Business events
        ViewEvents.SOURCE_DISCOVERED: 'business',
        ViewEvents.PROCESSING_STARTED: 'business',
        ViewEvents.PROCESSING_COMPLETED: 'business',
        
        # UI state
        ViewEvents.UI_STATE_CHANGED: 'ui_state',
        ViewEvents.STATUS_MESSAGE_CHANGED: 'ui_state',
        
        # Dialogs
        ViewEvents.FOLDER_DIALOG_REQUESTED: 'dialog',
        ViewEvents.ERROR_DIALOG_REQUESTED: 'dialog',
    }
    
    return category_map.get(event_type, 'unknown')
