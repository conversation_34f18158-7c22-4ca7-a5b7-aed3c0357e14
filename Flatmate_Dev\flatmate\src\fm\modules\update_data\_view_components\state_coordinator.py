"""
Simple State Coordinator for Update Data GUI.

Centralized state management following KISS principle.
Replaces the complex StateEngine with simple, testable state logic.

MIGRATION NOTE: Converted to event-driven architecture in Session 1.2
- Removed direct view/guide_pane coupling
- Uses local event bus for UI updates
- Preserves existing functionality through events
"""

from pathlib import Path
from typing import Optional

# Import local event bus and event data factory
from ..services.local_event_bus import update_data_local_bus, ViewEvents, EventDataFactory


class SimpleStateCoordinator:
    """
    Centralized state management for Update Data GUI.
    Encapsulates all UI state transitions in a single, testable location.

    MIGRATION: Now uses event-driven architecture instead of direct coupling.
    """

    def __init__(self):
        """
        Initialize state coordinator with event-driven architecture.

        MIGRATION NOTE: Removed view and guide_pane parameters.
        Now communicates via local event bus only.
        """
        self.local_bus = update_data_local_bus
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,  # 'folder' or 'files'
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }

        # Subscribe to business events that affect state
        self._subscribe_to_events()

    def _subscribe_to_events(self):
        """Subscribe to business events that affect state."""
        self.local_bus.subscribe(ViewEvents.SOURCE_DISCOVERED.value, self.on_source_discovered)
        self.local_bus.subscribe(ViewEvents.DESTINATION_CONFIGURED.value, self.on_destination_configured)
        self.local_bus.subscribe(ViewEvents.PROCESSING_STARTED.value, self.on_processing_started)
        self.local_bus.subscribe(ViewEvents.PROCESSING_COMPLETED.value, self.on_processing_completed)

    def on_source_discovered(self, source_data):
        """React to source discovery event."""
        if source_data['source_type'] == 'folder':
            self.set_source_folder(source_data['path'], source_data['file_count'])
        else:  # files
            self.set_source_files(source_data['files'])

    def set_source_folder(self, path: str, file_count: int):
        """Handle folder selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'folder',
            'source_path': path,
            'file_count': file_count
        })
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message(f"Found {file_count} files ready for processing")
    
    def set_source_files(self, files: list[str]):
        """Handle file selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'files',
            'source_path': files,
            'file_count': len(files)
        })
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message(f"Selected {len(files)} files for processing")

    def on_destination_configured(self, destination_data):
        """React to destination configuration event."""
        if destination_data['destination_type'] == 'same_as_source':
            self.set_destination_same_as_source()
        else:  # custom
            self.set_destination_custom(destination_data['path'])

    def set_destination_same_as_source(self):
        """Handle same-as-source destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': 'same_as_source'
        })
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message("Files will be archived in source folder")

    def set_destination_custom(self, path: str):
        """Handle custom destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': path
        })
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message(f"Files will be archived in {Path(path).name}")
    
    def is_ready_to_process(self) -> bool:
        """Check if all requirements are met."""
        return (self.state['source_configured'] and 
                self.state['destination_configured'] and 
                not self.state['processing'])
    
    def on_processing_started(self, processing_data):
        """React to processing started event."""
        self.start_processing()

    def start_processing(self):
        """Handle processing start."""
        self.state['processing'] = True
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message("Processing files...")

    def on_processing_completed(self, completion_data):
        """React to processing completed event."""
        success_count = completion_data.get('processed_count', 0)
        self.complete_processing(success_count)

    def complete_processing(self, success_count: int):
        """Handle processing completion."""
        self.state['processing'] = False
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message(f"Successfully processed {success_count} files")
    
    def reset_to_initial(self):
        """Reset to initial state."""
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
        self._update_ui_state()
        # MIGRATION: Emit guide message event instead of direct call
        self._emit_status_message("Select source files to begin")

    def _update_ui_state(self):
        """
        Central state transition logic.

        MIGRATION: Now emits UI state events instead of direct view manipulation.
        """
        ready_to_process = self.is_ready_to_process()

        # MIGRATION: Create UI state data and emit event instead of direct calls
        ui_state = EventDataFactory.ui_state_update(
            can_process=ready_to_process,
            status_message=self._get_current_status_message(),
            processing=self.state['processing'],
            archive_enabled=self.state['source_configured'],
            process_button_text=self._get_process_button_text()
        )

        self.local_bus.emit(ViewEvents.UI_STATE_CHANGED.value, ui_state)

    def _emit_status_message(self, message: str):
        """Emit status message event for guide pane."""
        self.local_bus.emit(ViewEvents.STATUS_MESSAGE_CHANGED.value, {
            'message': message,
            'timestamp': EventDataFactory.ui_state_update(True, message)['timestamp']
        })

    def _get_current_status_message(self) -> str:
        """Get current status message based on state."""
        if self.state['processing']:
            return "Processing files..."
        elif self.is_ready_to_process():
            return f"Ready to process {self.state['file_count']} files"
        elif self.state['source_configured'] and not self.state['destination_configured']:
            return "Select archive location to continue"
        elif not self.state['source_configured']:
            return "Select source files to begin"
        else:
            return "Configure settings to continue"

    def _get_process_button_text(self) -> str:
        """Get appropriate process button text."""
        if self.state['processing']:
            return "Processing..."
        elif self.is_ready_to_process():
            return "Process Files"
        else:
            return "Process Files"
