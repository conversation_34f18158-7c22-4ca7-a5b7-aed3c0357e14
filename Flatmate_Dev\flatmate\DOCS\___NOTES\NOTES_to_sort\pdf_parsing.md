# pdf_parsing

i have a question, i'm trying to reliably extract data from pdf bank account statements we have been trialing different packeges mostly pdf plumber and camelot
@test_PDF_PARSING results and discussion documents are in here.

plumbers output looks unusable
the jsons are a mare and more concerned with layout than actual text.
the text is missing values and there is no way of knowing what is a deposit a withdrawal or a balance from it
matching the json to the text output looks like a slog.

# Camelots output looks better: 

## page 1 lists the accounts contained in the pdf 

## pg 2  just has a name and some blah about switching to email
-  if this was printed and posted.
## pg 3 has `...W` at the top
`empty_row` 
Account name:, name
Product Name:, (account type - eg "free uP account"
Personalised Name: CURRENT
Account Number: 38-9004-0646977-00
Statement Period:, `empty_cell`,23 November 2024 to 22 December 2024
Date, Transaction, Withdrawls, Deposits, Balance. - (Which would map to: date, details, debit, credit, balance (db_names))
`empty_row`
<Transaction Data >

