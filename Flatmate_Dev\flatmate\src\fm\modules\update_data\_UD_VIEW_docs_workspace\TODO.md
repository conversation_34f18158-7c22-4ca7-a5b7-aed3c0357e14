``[] Check imports of ud_servies_events.py and other UD files renamed local_event_bus.py changed back was more an event types file ``
[] _review the case for Panel Switcher 
|

[]('C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view_components\center_panel_components\_switcher.py')

[] revisit inheritence system brought to atention by panel switcher error

[] consider breaking enum events out of local_event_bus.py into a separate file

[] consider moving local_event_bus.py to _view_components folder as view_events.py
it can still use event bus via composition
or inheritance discuss pros and cons - 
pro: keeps view logic closer to view ...


create a a new documentation protocol
that focuses specifically on a post  session session log in the current workspace docs folder (where ever that is )

the objective should be to leave things set up for the next session





