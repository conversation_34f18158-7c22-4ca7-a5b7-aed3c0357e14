File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_view.py", line 76, in _subscribe_to_events
    self.local_bus.subscribe(ViewEvents.FILES_DISPLAY_UPDATED.value, self.update_files_display)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^   
AttributeError: type object 'ViewEvents' has no attribute 'FILES_DISPLAY_UPDATED'

added to events enum + plus ome new speculative evnts I imagine we will need 