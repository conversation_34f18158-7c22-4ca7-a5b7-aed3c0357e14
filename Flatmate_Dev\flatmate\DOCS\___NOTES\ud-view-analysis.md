# ud-view-analysis

@/flatmate/DOCS/_PROTOCOLS/WORKFLOWS/k2-primer-hybrid-v1.md 


 the problem we are trying to selve is how best to implement 
 the _USER_JOURNEY_FLOW_v2 
 via a simple state manager in ud_view

the recent files on the subject are in C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\DOCS\_FEATURES\UD_DATA_gui\Refactor_take_2

The current docs on the subject are in:
update_data/_WORKSPACE

The "problem to be solved" is how to get the gui working properly witht he desired state management and C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\DOCS\_FEATURES\UD_DATA_gui\Refactor_take_2\user_journey_flow_v2.md 

via the @/flatmate/src/fm/modules/update_data/_view_components/state_coordinator.py 

I need a thorugh analysis of the current state  of the ud_view system, and suggested options for the optimal path forward.
