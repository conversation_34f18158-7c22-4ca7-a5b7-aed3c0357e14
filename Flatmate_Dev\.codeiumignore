{"exclude": ["**/__pycache__", "**/*.so", "**/.mypy_cache", "**/.pytest_cache", "**/.tox", "**/venv", "**/.venv*", "**/env", "**/env.bak", "**/venv.bak", "**/build", "**/dist", "**/*.egg-info", "**/*.egg", "**/node_modules", "**/.git", "**/.github", "**/.vscode", "**/.idea", "**/*.log", "**/logs", "**/dataset*", "**/migrations", "**/local_settings.py", "**/instance", "**/.coverage", "**/htmlcov", "**/coverage.xml", "**/.pytest_cache", "**/.mypy_cache", "**/*.sqlite3", "**/*.db", "**/src/data/transactions.db", "**/test_CSVs", "*.csv", "*.CSV", "**/z_archive*", "**/z_old_structure_backup", "**/z_proposed_view_interface", "**/.augment-guidelines", "**/.augment<PERSON>ore", "**/.kilocodemodes", "**/.kiloignore", "**/_MCP", "**/.AI_rules_other", "**/.augment", "**/.bmad-core", "**/.bmad-infrastructure-devops", "**/.kilocode", "**/.pytest_cache"]}